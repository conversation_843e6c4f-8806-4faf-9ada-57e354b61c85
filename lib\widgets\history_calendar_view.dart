import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import '../advanced_habit_analytics_service.dart';
import '../modern_theme.dart';

/// PHASE 2: Custom History Calendar View Widget
/// Implements the calendar-style heatmap described in implementing_metrics.md Section 2.1
/// This replaces the old BarChart with a visual calendar grid
class HistoryCalendarView extends StatelessWidget {
  final Map<DateTime, HistoryState> historyData;
  final Color primaryColor;
  final int weeksToShow;
  
  const HistoryCalendarView({
    super.key,
    required this.historyData,
    required this.primaryColor,
    this.weeksToShow = 12,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildWeekdayHeaders(theme),
        const SizedBox(height: 8),
        _buildCalendarGrid(theme),
      ],
    );
  }
  
  /// Build weekday headers (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, etc.)
  Widget _buildWeekdayHeaders(ThemeData theme) {
    const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    return Row(
      children: [
        const SizedBox(width: 40), // Space for month labels
        ...weekdays.map((day) => Expanded(
          child: Center(
            child: Text(
              day,
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        )),
      ],
    );
  }
  
  /// Build the main calendar grid
  Widget _buildCalendarGrid(ThemeData theme) {
    final weeks = _generateWeeks();
    
    return Column(
      children: [
        // Month headers
        _buildMonthHeaders(theme, weeks),
        const SizedBox(height: 4),
        
        // Calendar grid
        SizedBox(
          height: 140, // 7 rows × 20px per row
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Month labels column
              _buildMonthLabels(theme, weeks),
              
              // Calendar squares
              Expanded(
                child: _buildWeekColumns(theme, weeks),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  /// Build month headers above the calendar
  Widget _buildMonthHeaders(ThemeData theme, List<List<DateTime?>> weeks) {
    final headers = <Widget>[];
    String? currentMonth;
    int weekCount = 0;
    
    // Space for month labels
    headers.add(const SizedBox(width: 40));
    
    for (final week in weeks) {
      final firstDate = week.firstWhere((date) => date != null, orElse: () => null);
      if (firstDate != null) {
        final monthName = _getMonthName(firstDate);
        
        if (currentMonth != monthName) {
          if (currentMonth != null && weekCount > 0) {
            headers.add(
              SizedBox(
                width: weekCount * 20.0,
                child: Text(
                  currentMonth,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            );
          }
          currentMonth = monthName;
          weekCount = 1;
        } else {
          weekCount++;
        }
      }
    }
    
    // Add final month header
    if (currentMonth != null && weekCount > 0) {
      headers.add(
        SizedBox(
          width: weekCount * 20.0,
          child: Text(
            currentMonth,
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: headers,
    );
  }
  
  /// Build month labels on the left side
  Widget _buildMonthLabels(ThemeData theme, List<List<DateTime?>> weeks) {
    return SizedBox(
      width: 40,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(7, (dayIndex) {
          // Only show month label on the first row
          if (dayIndex == 0 && weeks.isNotEmpty) {
            final firstWeek = weeks.first;
            final firstDate = firstWeek.firstWhere((date) => date != null, orElse: () => null);
            if (firstDate != null) {
              return Container(
                height: 20,
                alignment: Alignment.centerRight,
                child: Text(
                  _getMonthName(firstDate),
                  style: GoogleFonts.inter(
                    fontSize: 9,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              );
            }
          }
          return const SizedBox(height: 20);
        }),
      ),
    );
  }
  
  /// Build week columns (7 rows × N columns)
  Widget _buildWeekColumns(ThemeData theme, List<List<DateTime?>> weeks) {
    return Row(
      children: weeks.map((week) => _buildWeekColumn(theme, week)).toList(),
    );
  }
  
  /// Build a single week column (7 squares vertically)
  Widget _buildWeekColumn(ThemeData theme, List<DateTime?> week) {
    return Container(
      width: 18,
      margin: const EdgeInsets.only(right: 2),
      child: Column(
        children: week.map((date) => _buildDaySquare(theme, date)).toList(),
      ),
    );
  }
  
  /// Build a single day square
  Widget _buildDaySquare(ThemeData theme, DateTime? date) {
    final state = date != null ? (historyData[date] ?? HistoryState.NOT_COMPLETED) : HistoryState.NOT_APPLICABLE;
    
    return Container(
      width: 18,
      height: 18,
      margin: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(
        color: _getSquareColor(theme, state),
        borderRadius: BorderRadius.circular(3),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: date != null ? Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.inter(
            fontSize: 8,
            fontWeight: FontWeight.w500,
            color: _getTextColor(theme, state),
          ),
        ),
      ) : null,
    );
  }
  
  /// Get color for square based on history state
  Color _getSquareColor(ThemeData theme, HistoryState state) {
    switch (state) {
      case HistoryState.COMPLETED:
        return primaryColor;
      case HistoryState.PARTIALLY_COMPLETED:
        return primaryColor.withOpacity(0.6);
      case HistoryState.NOT_COMPLETED:
        return theme.colorScheme.surface;
      case HistoryState.NOT_APPLICABLE:
        return Colors.transparent;
    }
  }
  
  /// Get text color for square based on history state
  Color _getTextColor(ThemeData theme, HistoryState state) {
    switch (state) {
      case HistoryState.COMPLETED:
        return Colors.white;
      case HistoryState.PARTIALLY_COMPLETED:
        return Colors.white;
      case HistoryState.NOT_COMPLETED:
        return theme.colorScheme.onSurface;
      case HistoryState.NOT_APPLICABLE:
        return theme.colorScheme.onSurfaceVariant.withOpacity(0.5);
    }
  }
  
  /// Generate weeks for the calendar
  List<List<DateTime?>> _generateWeeks() {
    if (historyData.isEmpty) return [];
    
    final dates = historyData.keys.toList()..sort();
    final startDate = dates.first;
    final endDate = dates.last;
    
    // Start from the beginning of the week containing startDate
    final firstWeekday = startDate.weekday % 7; // Convert to 0-6 (Sun-Sat)
    DateTime weekStart = startDate.subtract(Duration(days: firstWeekday));
    
    final weeks = <List<DateTime?>>[];
    
    while (weekStart.isBefore(endDate.add(const Duration(days: 7)))) {
      final week = <DateTime?>[];
      
      for (int i = 0; i < 7; i++) {
        final date = weekStart.add(Duration(days: i));
        
        if (date.isBefore(startDate) || date.isAfter(endDate)) {
          week.add(null); // Empty cell
        } else {
          week.add(date);
        }
      }
      
      weeks.add(week);
      weekStart = weekStart.add(const Duration(days: 7));
      
      // Prevent infinite loop
      if (weeks.length > 20) break;
    }
    
    return weeks;
  }
  
  /// Get month name
  String _getMonthName(DateTime date) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[date.month - 1];
  }
}