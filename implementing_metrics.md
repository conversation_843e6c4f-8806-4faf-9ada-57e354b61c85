# Loop Habit Tracker - Implementing Metrics and Charts

## Executive Summary

Loop Habit Tracker implements a sophisticated metrics system with two primary visualization components: **Score Charts** and **History Charts**. This report provides a comprehensive analysis of how these metrics are calculated, stored, and displayed for individual habits.

## Architecture Overview

The application follows a clean architecture pattern with clear separation between:
- **Core Models** (`uhabits-core`): Business logic and data models
- **Android UI** (`uhabits-android`): Platform-specific UI implementations
- **Cross-platform Views** (`uhabits-core/ui/views`): Reusable chart components

## 1. Score System Implementation

### 1.1 Core Score Calculation

**Location**: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/models/Score.kt`

The score system uses an advanced mathematical formula to calculate habit strength:

```kotlin
fun compute(frequency: Double, previousScore: Double, checkmarkValue: Double): Double {
    val multiplier = 0.5.pow(sqrt(frequency) / 13.0)
    var score = previousScore * multiplier
    score += checkmarkValue * (1 - multiplier)
    return score
}
```

**Key Components**:
- **Multiplier**: `0.5^(sqrt(frequency) / 13.0)` - Controls how much previous scores decay
- **Frequency**: Number of repetitions divided by interval length (e.g., 3/8 = 0.375 for 3 times in 8 days)
- **Checkmark Value**: 0.0 to 1.0 representing completion percentage
- **Score Range**: 0.0 to 1.0 (0% to 100% strength)

### 1.2 Score List Management

**Location**: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/models/ScoreList.kt`

The `ScoreList` class manages score computation and caching:

```kotlin
@ThreadSafe
class ScoreList {
    private val map = HashMap<Timestamp, Score>()
    
    fun recompute(frequency: Frequency, isNumerical: Boolean, 
                  numericalHabitType: NumericalHabitType, targetValue: Double,
                  computedEntries: EntryList, from: Timestamp, to: Timestamp)
}
```

**Features**:
- Thread-safe HashMap for score caching
- Supports both boolean and numerical habits
- Handles different numerical habit types (AT_LEAST, AT_MOST)
- Rolling sum calculation for performance
- Automatic recomputation when entries change

### 1.3 Score Chart Visualization

**Android Implementation**: `uhabits-android/src/main/java/org/isoron/uhabits/activities/common/views/ScoreChart.kt`

**Key Features**:
- Extends `ScrollableChart` for horizontal scrolling
- Line chart with circular markers
- Configurable bucket sizes (1, 7, 31, 92, 365 days)
- Grid lines showing percentage levels
- Date labels with smart formatting
- Transparency support for widgets

**Drawing Process**:
1. **Grid Drawing**: Horizontal lines at 20%, 40%, 60%, 80%, 100%
2. **Line Drawing**: Connects score points with smooth lines
3. **Marker Drawing**: Circular indicators at each data point
4. **Footer Drawing**: Date labels with month/year information

### 1.4 Score Card Integration

**Core Presenter**: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/ui/screens/habits/show/views/ScoreCard.kt`

**Android View**: `uhabits-android/src/main/java/org/isoron/uhabits/activities/habits/show/views/ScoreCardView.kt`

**Bucket Sizes**:
- **Daily** (1): Individual day scores
- **Weekly** (7): Average scores per week
- **Monthly** (31): Average scores per month  
- **Quarterly** (92): Average scores per quarter
- **Yearly** (365): Average scores per year

## 2. History Chart Implementation

### 2.1 Core History Chart

**Location**: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/ui/views/HistoryChart.kt`

The history chart displays habit completion data in a calendar-like grid format:

```kotlin
class HistoryChart(
    var dateFormatter: LocalDateFormatter,
    var firstWeekday: DayOfWeek,
    var paletteColor: PaletteColor,
    var series: List<Square>,
    var defaultSquare: Square,
    var notesIndicators: List<Boolean>,
    var theme: Theme,
    var today: LocalDate
) : DataView
```

**Square Types**:
- **ON**: Habit completed (full color)
- **OFF**: Habit not completed (light gray)
- **GREY**: Not applicable day (medium gray)
- **DIMMED**: Partially completed (blended color)
- **HATCHED**: Special state with diagonal lines

### 2.2 History Chart Drawing

**Key Drawing Methods**:

```kotlin
override fun draw(canvas: Canvas) {
    // 1. Calculate layout dimensions
    squareSize = round((height - 2 * padding) / 8.0)
    nColumns = floor((width - 2 * padding - weekdayColumnWidth) / squareSize).toInt()
    
    // 2. Draw main columns (weeks)
    repeat(nColumns) { column ->
        drawColumn(canvas, column, topDate, topOffset)
    }
    
    // 3. Draw weekday labels
    repeat(7) { row ->
        canvas.drawText(dateFormatter.shortWeekdayName(date), x, y)
    }
}
```

**Features**:
- **Calendar Layout**: 7 rows (days of week) × N columns (weeks)
- **Responsive Design**: Adapts to available screen space
- **Click Handling**: Supports both short and long press events
- **Notes Indicators**: Small circles for days with notes
- **Month Headers**: Smart month/year label positioning

### 2.3 History Card Integration

**Core Presenter**: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/ui/screens/habits/show/views/HistoryCard.kt`

**Android View**: `uhabits-android/src/main/java/org/isoron/uhabits/activities/habits/show/views/HistoryCardView.kt`

**State Management**:
```kotlin
data class HistoryCardState(
    val color: PaletteColor,
    val firstWeekday: DayOfWeek,
    val series: List<HistoryChart.Square>,
    val defaultSquare: HistoryChart.Square,
    val notesIndicators: List<Boolean>,
    val theme: Theme,
    val today: LocalDate
)
```

## 3. Data Flow Architecture

### 3.1 Entry System

**Base Entry Model**: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/models/Entry.kt`

```kotlin
data class Entry(
    val timestamp: Timestamp,
    val value: Int,
    val notes: String = ""
)
```

**Entry Values**:
- `YES_MANUAL = 2`: User manually marked as completed
- `YES_AUTO = 1`: Automatically marked (frequency-based)
- `NO = 0`: Not completed when expected
- `SKIP = 3`: Not applicable for this day
- `UNKNOWN = -1`: No data available

### 3.2 Entry List Processing

**Location**: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/models/EntryList.kt`

**Key Features**:
- **Original Entries**: Raw user input data
- **Computed Entries**: Processed entries considering frequency
- **Thread-safe operations**: Synchronized access to entry data
- **Interval queries**: Efficient range-based data retrieval

### 3.3 Habit Recomputation

**Location**: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/models/Habit.kt`

```kotlin
fun recompute() {
    // 1. Recompute entries based on frequency
    computedEntries.recomputeFrom(originalEntries, frequency, isNumerical)
    
    // 2. Recompute scores from entries
    scores.recompute(frequency, isNumerical, numericalHabitType, 
                    targetValue, computedEntries, from, to)
    
    // 3. Recompute streaks
    streaks.recompute(computedEntries, from, to, isNumerical, 
                     targetValue, targetType)
}
```

## 4. Widget Implementation

### 4.1 Score Widget

**Location**: `uhabits-android/src/main/java/org/isoron/uhabits/widgets/ScoreWidget.kt`

```kotlin
class ScoreWidget(context: Context, id: Int, private val habit: Habit, stacked: Boolean = false) : BaseWidget(context, id, stacked) {
    
    override fun refreshData(view: View) {
        val viewModel = ScoreCardPresenter.buildState(habit, firstWeekday, spinnerPosition, WidgetTheme())
        val widgetView = view as GraphWidgetView
        (widgetView.dataView as ScoreChart).apply {
            setIsTransparencyEnabled(true)
            setBucketSize(viewModel.bucketSize)
            setColor(WidgetTheme().color(habit.color).toInt())
            setScores(viewModel.scores)
        }
    }
}
```

### 4.2 History Widget

**Location**: `uhabits-android/src/main/java/org/isoron/uhabits/widgets/HistoryWidget.kt`

```kotlin
class HistoryWidget(context: Context, id: Int, private val habit: Habit, stacked: Boolean = false) : BaseWidget(context, id, stacked) {
    
    override fun refreshData(view: View) {
        val model = HistoryCardPresenter.buildState(habit, prefs.firstWeekday, WidgetTheme())
        (widgetView.dataView as AndroidDataView).apply {
            val historyChart = (this.view as HistoryChart)
            historyChart.series = model.series
            historyChart.defaultSquare = model.defaultSquare
            historyChart.notesIndicators = model.notesIndicators
        }
    }
}
```

## 5. User Interface Integration

### 5.1 Show Habit Activity

**Location**: `uhabits-android/src/main/java/org/isoron/uhabits/activities/habits/show/ShowHabitActivity.kt`

The main activity that displays habit details integrates both score and history charts:

- **Score Card**: Shows habit strength over time with configurable time periods
- **History Card**: Shows daily completion status in calendar format
- **Interactive Elements**: Click handlers for editing entries and viewing details

### 5.2 Layout Files

**Score Card Layout**: `uhabits-android/src/main/res/layout/show_habit_score.xml`
**History Card Layout**: `uhabits-android/src/main/res/layout/show_habit_history.xml`

## 6. Performance Optimizations

### 6.1 Caching Strategy

- **Score Caching**: HashMap-based caching in `ScoreList`
- **Entry Caching**: Efficient interval-based queries
- **Widget Caching**: Bitmap caching for transparency effects
- **Lazy Computation**: Scores computed only when needed

### 6.2 Memory Management

- **Thread Safety**: Synchronized access to shared data structures
- **Bitmap Recycling**: Proper cleanup of drawing caches
- **Efficient Data Structures**: Use of appropriate collections for performance

## 7. Testing Strategy

### 7.1 Unit Tests

**Score Tests**: `uhabits-core/src/jvmTest/java/org/isoron/uhabits/core/models/ScoreTest.kt`
**History Chart Tests**: `uhabits-core/src/jvmTest/java/org/isoron/uhabits/core/ui/views/HistoryChartTest.kt`

### 7.2 UI Tests

**Score Chart Tests**: `uhabits-android/src/androidTest/java/org/isoron/uhabits/activities/common/views/ScoreChartTest.kt`
**Widget Tests**: Comprehensive testing of widget functionality

## 8. Key Design Patterns

### 8.1 MVP (Model-View-Presenter)

- **Models**: Core data classes (`Habit`, `Score`, `Entry`)
- **Views**: Android UI components (`ScoreCardView`, `HistoryCardView`)
- **Presenters**: Business logic coordinators (`ScoreCardPresenter`, `HistoryCardPresenter`)

### 8.2 Observer Pattern

- **ModelObservable**: Notifies UI components of data changes
- **Automatic Updates**: Charts refresh when underlying data changes

### 8.3 Strategy Pattern

- **Theme Support**: Different visual themes (light, dark, widget)
- **Chart Types**: Pluggable chart implementations

## 9. Extensibility Points

### 9.1 Adding New Chart Types

1. Implement `DataView` interface
2. Create corresponding presenter and state classes
3. Add Android-specific view wrapper
4. Integrate with habit detail screen

### 9.2 Custom Metrics

1. Extend score calculation in `Score.compute()`
2. Add new fields to `ScoreCardState`
3. Update chart rendering logic
4. Add configuration options

## 10. Conclusion

Loop Habit Tracker's metrics implementation demonstrates sophisticated software engineering with:

- **Mathematical Rigor**: Advanced scoring algorithm that realistically models habit strength
- **Clean Architecture**: Clear separation of concerns between data, business logic, and UI
- **Performance Focus**: Efficient caching and computation strategies
- **User Experience**: Intuitive visualizations that help users understand their progress
- **Extensibility**: Well-designed patterns that allow for future enhancements

The combination of score charts (showing trend analysis) and history charts (showing detailed daily data) provides users with comprehensive insights into their habit formation journey, making this one of the most analytically robust habit tracking applications available.