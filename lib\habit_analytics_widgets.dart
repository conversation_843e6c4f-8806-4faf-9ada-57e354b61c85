import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:developer' as developer;
import 'habit_analytics_service.dart';
import 'modern_theme.dart';

/// Comprehensive Score Chart Widget with all requirements implemented
class RobustScoreChart extends StatefulWidget {
  final HabitAnalyticsService analyticsService;
  final TimeScale timeScale;
  final Function(TimeScale) onTimeScaleChanged;

  const RobustScoreChart({
    super.key,
    required this.analyticsService,
    required this.timeScale,
    required this.onTimeScaleChanged,
  });

  @override
  State<RobustScoreChart> createState() => _RobustScoreChartState();
}

class _RobustScoreChartState extends State<RobustScoreChart> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    
    // Initial scroll position to show most recent data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToRecentData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToRecentData() {
    if (_scrollController.hasClients) {
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      if (maxScrollExtent > 0) {
        _scrollController.jumpTo(maxScrollExtent);
        developer.log('Score Chart scrolled to recent data at offset: $maxScrollExtent', name: 'ChartScroll');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(ModernTheme.spaceSM),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          SizedBox(height: ModernTheme.spaceSM),
          SizedBox(
            height: 200,
            child: _buildChartWithStickyYAxis(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Score Chart',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        _buildTimeScaleButton(theme),
      ],
    );
  }

  Widget _buildTimeScaleButton(ThemeData theme) {
    return OutlinedButton(
      onPressed: () => _showTimeScaleMenu(theme),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(
          horizontal: ModernTheme.spaceSM,
          vertical: ModernTheme.spaceXS,
        ),
        minimumSize: Size.zero,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _getTimeScaleDisplayName(widget.timeScale),
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: 4),
          Icon(Icons.keyboard_arrow_down, size: 16),
        ],
      ),
    );
  }

  void _showTimeScaleMenu(ThemeData theme) {
    showMenu(
      context: context,
      position: const RelativeRect.fromLTRB(100, 100, 0, 0),
      items: TimeScale.values
          .map(
            (scale) => PopupMenuItem(
              value: scale,
              child: Text(_getTimeScaleDisplayName(scale)),
            ),
          )
          .toList(),
    ).then((value) {
      if (value != null) {
        widget.onTimeScaleChanged(value);
        // Re-scroll to recent data after time scale change
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToRecentData();
        });
      }
    });
  }

  String _getTimeScaleDisplayName(TimeScale scale) {
    switch (scale) {
      case TimeScale.day:
        return 'Day';
      case TimeScale.week:
        return 'Week';
      case TimeScale.month:
        return 'Month';
      case TimeScale.quarter:
        return 'Quarter';
      case TimeScale.year:
        return 'Year';
    }
  }

  /// Layout Structure: Sticky Y-Axis implementation
  Widget _buildChartWithStickyYAxis(ThemeData theme) {
    return Row(
      children: [
        // First child: Fixed width container for Y-axis titles only
        SizedBox(
          width: 65.0,
          child: _buildStickyYAxis(theme),
        ),
        // Second child: Expanded widget with horizontal scroll for main chart
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: SizedBox(
              width: _calculateChartWidth(),
              child: _buildMainChart(theme),
            ),
          ),
        ),
      ],
    );
  }

  /// Sticky Y-axis showing only leftTitles
  Widget _buildStickyYAxis(ThemeData theme) {
    final dataPoints = widget.analyticsService.getRobustScoreDataForChart(widget.timeScale);
    if (dataPoints.isEmpty) return Container();

    // Dynamic Y-axis calculation with guardrail
    final maxDataValue = dataPoints
        .where((point) => !point.value.isNaN && !point.value.isInfinite)
        .map((point) => point.value)
        .fold(0.0, (max, value) => value > max ? value : max);
    
    var minY = 0.0;
    var maxY = (maxDataValue * 1.2).clamp(0.1, 1.0);
    
    // Guardrail: prevent NaN crash
    if (minY == maxY) {
      maxY = minY + 0.2;
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        maxY: maxY,
        minY: minY,
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          leftTitles: AxisTitles(
            axisNameWidget: Text(
              'Score',
              style: GoogleFonts.inter(
                fontSize: 10,
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${(value * 100).toInt()}%',
                  style: GoogleFonts.inter(
                    fontSize: 9,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [], // No data lines, just Y-axis
      ),
    );
  }

  /// Main chart with leftTitles hidden to avoid duplication
  Widget _buildMainChart(ThemeData theme) {
    final dataPoints = widget.analyticsService.getRobustScoreDataForChart(widget.timeScale);
    
    if (dataPoints.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: GoogleFonts.inter(color: theme.colorScheme.onSurfaceVariant),
        ),
      );
    }

    // Dynamic Y-axis calculation with guardrail
    final maxDataValue = dataPoints
        .where((point) => !point.value.isNaN && !point.value.isInfinite)
        .map((point) => point.value)
        .fold(0.0, (max, value) => value > max ? value : max);
    
    var minY = 0.0;
    var maxY = (maxDataValue * 1.2).clamp(0.1, 1.0);
    
    // Guardrail: prevent NaN crash
    if (minY == maxY) {
      maxY = minY + 0.2;
    }

    developer.log('Score Chart Y-axis range: minY=$minY, maxY=$maxY', name: 'ChartYAxis');

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        maxY: maxY,
        minY: minY,
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // Hidden to avoid duplication
          bottomTitles: AxisTitles(
            axisNameWidget: Text(
              _getTimeScaleDisplayName(widget.timeScale),
              style: GoogleFonts.inter(
                fontSize: 10,
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < dataPoints.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      dataPoints[index].label,
                      style: GoogleFonts.inter(
                        fontSize: 9,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: _generateFlSpots(dataPoints),
            isCurved: false,
            color: theme.colorScheme.primary,
            barWidth: 3.0,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: theme.colorScheme.primary,
                  strokeWidth: 2,
                  strokeColor: theme.colorScheme.surface,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Generate FlSpots with line termination logic
  List<FlSpot> _generateFlSpots(List<ChartDataPoint> dataPoints) {
    final spots = <FlSpot>[];
    
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      
      // Handle line termination: insert null spot after last real data point
      if (point.value.isNaN) {
        spots.add(FlSpot(i.toDouble(), double.nan)); // Line break
      } else {
        spots.add(FlSpot(i.toDouble(), point.value.clamp(0.0, 1.0)));
      }
    }
    
    developer.log('Generated ${spots.length} FlSpots for Score Chart', name: 'ChartSpots');
    return spots;
  }

  double _calculateChartWidth() {
    switch (widget.timeScale) {
      case TimeScale.day:
        return 35.0 * 32; // 30 days + 2 future days
      case TimeScale.week:
        return 50.0 * 14; // 12 weeks + 2 future weeks
      case TimeScale.month:
        return 60.0 * 14; // 12 months + 2 future months
      case TimeScale.quarter:
        return 80.0 * 10; // 8 quarters + 2 future quarters
      case TimeScale.year:
        return 100.0 * 7; // 5 years + 2 future years
    }
  }
}

/// Comprehensive History Chart Widget with all requirements implemented
class RobustHistoryChart extends StatefulWidget {
  final HabitAnalyticsService analyticsService;
  final TimeScale timeScale;
  final Function(TimeScale) onTimeScaleChanged;

  const RobustHistoryChart({
    super.key,
    required this.analyticsService,
    required this.timeScale,
    required this.onTimeScaleChanged,
  });

  @override
  State<RobustHistoryChart> createState() => _RobustHistoryChartState();
}

class _RobustHistoryChartState extends State<RobustHistoryChart> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    
    // Initial scroll position to show most recent data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToRecentData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToRecentData() {
    if (_scrollController.hasClients) {
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      if (maxScrollExtent > 0) {
        _scrollController.jumpTo(maxScrollExtent);
        developer.log('History Chart scrolled to recent data at offset: $maxScrollExtent', name: 'ChartScroll');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(ModernTheme.spaceSM),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          SizedBox(height: ModernTheme.spaceSM),
          SizedBox(
            height: 200,
            child: _buildChartWithStickyYAxis(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'History Chart',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        _buildTimeScaleButton(theme),
      ],
    );
  }

  Widget _buildTimeScaleButton(ThemeData theme) {
    return OutlinedButton(
      onPressed: () => _showTimeScaleMenu(theme),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(
          horizontal: ModernTheme.spaceSM,
          vertical: ModernTheme.spaceXS,
        ),
        minimumSize: Size.zero,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _getTimeScaleDisplayName(widget.timeScale),
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: 4),
          Icon(Icons.keyboard_arrow_down, size: 16),
        ],
      ),
    );
  }

  void _showTimeScaleMenu(ThemeData theme) {
    showMenu(
      context: context,
      position: const RelativeRect.fromLTRB(100, 100, 0, 0),
      items: TimeScale.values
          .map(
            (scale) => PopupMenuItem(
              value: scale,
              child: Text(_getTimeScaleDisplayName(scale)),
            ),
          )
          .toList(),
    ).then((value) {
      if (value != null) {
        widget.onTimeScaleChanged(value);
        // Re-scroll to recent data after time scale change
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToRecentData();
        });
      }
    });
  }

  String _getTimeScaleDisplayName(TimeScale scale) {
    switch (scale) {
      case TimeScale.day:
        return 'Day';
      case TimeScale.week:
        return 'Week';
      case TimeScale.month:
        return 'Month';
      case TimeScale.quarter:
        return 'Quarter';
      case TimeScale.year:
        return 'Year';
    }
  }

  /// Layout Structure: Sticky Y-Axis implementation
  Widget _buildChartWithStickyYAxis(ThemeData theme) {
    return Row(
      children: [
        // First child: Fixed width container for Y-axis titles only
        SizedBox(
          width: 65.0,
          child: _buildStickyYAxis(theme),
        ),
        // Second child: Expanded widget with horizontal scroll for main chart
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: SizedBox(
              width: _calculateChartWidth(),
              child: _buildMainChart(theme),
            ),
          ),
        ),
      ],
    );
  }

  /// Sticky Y-axis showing only leftTitles
  Widget _buildStickyYAxis(ThemeData theme) {
    final dataPoints = widget.analyticsService.getRobustHistoryDataForChart(widget.timeScale);
    if (dataPoints.isEmpty) return Container();

    // Dynamic Y-axis calculation with guardrail
    final maxDataValue = dataPoints
        .map((point) => point.value)
        .fold(0.0, (max, value) => value > max ? value : max);
    
    var minY = 0.0;
    var maxY = (maxDataValue * 1.2).clamp(1.0, double.infinity);
    
    // Guardrail: prevent NaN crash
    if (minY == maxY) {
      maxY = minY + 1.0;
    }

    return BarChart(
      BarChartData(
        gridData: FlGridData(show: false),
        maxY: maxY,
        minY: minY,
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          leftTitles: AxisTitles(
            axisNameWidget: Text(
              'Count',
              style: GoogleFonts.inter(
                fontSize: 10,
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: GoogleFonts.inter(
                    fontSize: 9,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        barGroups: [], // No data bars, just Y-axis
      ),
    );
  }

  /// Main chart with leftTitles hidden to avoid duplication
  Widget _buildMainChart(ThemeData theme) {
    final dataPoints = widget.analyticsService.getRobustHistoryDataForChart(widget.timeScale);
    
    if (dataPoints.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: GoogleFonts.inter(color: theme.colorScheme.onSurfaceVariant),
        ),
      );
    }

    // Dynamic Y-axis calculation with guardrail
    final maxDataValue = dataPoints
        .map((point) => point.value)
        .fold(0.0, (max, value) => value > max ? value : max);
    
    var minY = 0.0;
    var maxY = (maxDataValue * 1.2).clamp(1.0, double.infinity);
    
    // Guardrail: prevent NaN crash
    if (minY == maxY) {
      maxY = minY + 1.0;
    }

    developer.log('History Chart Y-axis range: minY=$minY, maxY=$maxY', name: 'ChartYAxis');

    return BarChart(
      BarChartData(
        gridData: FlGridData(show: false),
        maxY: maxY,
        minY: minY,
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // Hidden to avoid duplication
          bottomTitles: AxisTitles(
            axisNameWidget: Text(
              _getTimeScaleDisplayName(widget.timeScale),
              style: GoogleFonts.inter(
                fontSize: 10,
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 35,
              interval: 1,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < dataPoints.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 6),
                    child: Text(
                      dataPoints[index].label,
                      style: GoogleFonts.inter(
                        fontSize: 9,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        // Bar Labels: Numerical labels on top of each bar
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            tooltipBorder: BorderSide.none,
            tooltipPadding: EdgeInsets.zero,
            tooltipMargin: 8,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final count = rod.toY.round();
              if (count == 0) return null; // Don't show labels for zero values
              
              return BarTooltipItem(
                count.toString(),
                TextStyle(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  backgroundColor: Colors.transparent, // Transparent background
                ),
              );
            },
          ),
        ),
        barGroups: _generateBarGroups(dataPoints, theme),
      ),
    );
  }

  /// Generate bar groups for the chart
  List<BarChartGroupData> _generateBarGroups(List<ChartDataPoint> dataPoints, ThemeData theme) {
    final barGroups = <BarChartGroupData>[];
    
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      
      barGroups.add(
        BarChartGroupData(
          x: i,
          barsSpace: 8,
          barRods: [
            BarChartRodData(
              toY: point.value,
              color: theme.colorScheme.primary,
              width: 20,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      );
    }
    
    developer.log('Generated ${barGroups.length} bar groups for History Chart', name: 'ChartBars');
    return barGroups;
  }

  double _calculateChartWidth() {
    switch (widget.timeScale) {
      case TimeScale.day:
        return 35.0 * 32; // 30 days + 2 future days
      case TimeScale.week:
        return 50.0 * 14; // 12 weeks + 2 future weeks
      case TimeScale.month:
        return 60.0 * 14; // 12 months + 2 future months
      case TimeScale.quarter:
        return 80.0 * 10; // 8 quarters + 2 future quarters
      case TimeScale.year:
        return 100.0 * 7; // 5 years + 2 future years
    }
  }
}