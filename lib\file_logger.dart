import 'dart:io';

/// Dedicated logging service class for writing logs to a file
class FileLogger {
  /// Single static method to log messages to errors/ScoreCardLogs.txt
  static Future<void> log(String message) async {
    try {
      // Use current directory (workspace) + errors folder
      final currentDir = Directory.current;
      final errorsDir = Directory('${currentDir.path}/errors');
      
      // Ensure the errors directory exists
      if (!await errorsDir.exists()) {
        await errorsDir.create(recursive: true);
        print('[FILE_LOGGER] Created errors directory: ${errorsDir.path}');
      }
      
      // Create a reference to the log file
      final file = File('${errorsDir.path}/ScoreCardLogs.txt');
      
      // Create timestamp and format log message
      final timestamp = DateTime.now().toIso8601String();
      final logMessage = '[$timestamp] $message\n';
      
      // Append the message to the log file
      await file.writeAsString(logMessage, mode: FileMode.append);
      
      // Also print to console for immediate feedback
      print('[FILE_LOGGER] Written to ${file.path}: $message');
    } catch (e) {
      // Fallback to console logging if file logging fails
      print('[FILE_LOGGER_ERROR] Failed to write to log file: $e');
      print('[FILE_LOGGER_FALLBACK] $message');
    }
  }
}