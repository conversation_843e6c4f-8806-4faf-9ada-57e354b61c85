import 'dart:math' as math;
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'habit.dart';
import 'entry.dart';
import 'file_logger.dart';

/// History state for calendar heatmap squares
enum HistoryState {
  COMPLETED,
  PARTIALLY_COMPLETED,
  NOT_COMPLETED,
  NOT_APPLICABLE,
}

/// Advanced data point for charts with enhanced metadata
class AdvancedChartDataPoint {
  final DateTime date;
  final double value;
  final String label;
  final HistoryState? historyState; // For history charts
  
  AdvancedChartDataPoint({
    required this.date,
    required this.value,
    required this.label,
    this.historyState,
  });
}

/// Advanced Habit Analytics Service implementing Loop Habit Tracker algorithms
/// This service replaces the current analytics with mathematically sound calculations
/// inspired by implementing_metrics.md
class AdvancedHabitAnalyticsService {
  final Habit habit;
  final List<Entry> entries;
  
  // Cache for performance
  final Map<DateTime, double> _scoreCache = {};
  final Map<DateTime, HistoryState> _historyCache = {};
  
  AdvancedHabitAnalyticsService({
    required this.habit,
    required this.entries,
  }) {
    _initializeCache();
  }
  
  /// Initialize cache for better performance
  void _initializeCache() {
    developer.log('=== INITIALIZING ADVANCED ANALYTICS CACHE ===', name: 'AdvancedAnalytics');
    developer.log('Habit: ${habit.name}', name: 'AdvancedAnalytics');
    developer.log('Total entries: ${entries.length}', name: 'AdvancedAnalytics');
    
    // Pre-compute history states for all entry dates
    for (final entry in entries) {
      final date = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      _historyCache[date] = _computeHistoryState(entry);
    }
    
    developer.log('History cache initialized with ${_historyCache.length} dates', name: 'AdvancedAnalytics');
  }
  
  /// PHASE 1: Core Score Calculation
  /// Implements the precise formula from implementing_metrics.md Section 1.1:
  /// score = previousScore × multiplier + checkmarkValue × (1 − multiplier)
  /// where multiplier = 0.5^(sqrt(frequency) / 13.0)
  double computeHabitStrengthScore(DateTime targetDate, {double frequency = 1.0}) {
    developer.log('=== COMPUTING HABIT STRENGTH SCORE ===', name: 'AdvancedAnalytics');
    developer.log('Target date: ${targetDate.toIso8601String()}', name: 'AdvancedAnalytics');
    developer.log('Frequency: $frequency', name: 'AdvancedAnalytics');
    
    // Check cache first
    final cacheKey = DateTime(targetDate.year, targetDate.month, targetDate.day);
    if (_scoreCache.containsKey(cacheKey)) {
      final cachedScore = _scoreCache[cacheKey]!;
      developer.log('Returning cached score: $cachedScore', name: 'AdvancedAnalytics');
      return cachedScore;
    }
    
    // Get all entries up to target date, sorted chronologically
    final relevantEntries = entries.where((entry) {
      return entry.timestamp.isBefore(targetDate.add(const Duration(days: 1)));
    }).toList();
    
    if (relevantEntries.isEmpty) {
      developer.log('No entries found, returning score: 0.0', name: 'AdvancedAnalytics');
      _scoreCache[cacheKey] = 0.0;
      return 0.0;
    }
    
    relevantEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    // Calculate multiplier using Loop Habit Tracker formula
    final multiplier = math.pow(0.5, math.sqrt(frequency) / 13.0).toDouble();
    developer.log('Calculated multiplier: $multiplier', name: 'AdvancedAnalytics');
    
    double score = 0.0;
    
    // Process each entry chronologically
    for (int i = 0; i < relevantEntries.length; i++) {
      final entry = relevantEntries[i];
      final checkmarkValue = _getCheckmarkValue(entry);
      
      // Apply Loop Habit Tracker formula
      final previousScore = score;
      score = previousScore * multiplier + checkmarkValue * (1 - multiplier);
      
      developer.log(
        'Entry ${i + 1}: date=${entry.timestamp.toIso8601String()}, '
        'checkmark=$checkmarkValue, previousScore=$previousScore, newScore=$score',
        name: 'AdvancedAnalytics'
      );
    }
    
    // Clamp to valid range
    score = score.clamp(0.0, 1.0);
    
    // Cache the result
    _scoreCache[cacheKey] = score;
    
    developer.log('Final computed score: $score', name: 'AdvancedAnalytics');
    developer.log('DEBUG Score Chart Data: [${targetDate.toIso8601String()}: $score]', name: 'AdvancedAnalytics');
    
    return score;
  }
  
  /// PHASE 1: Generate Score Chart Data
  /// Returns time-series data for the new Score Chart
  List<AdvancedChartDataPoint> generateScoreChartData({
    int daysBack = 30,
    int bucketSize = 1, // 1=daily, 7=weekly, 31=monthly
  }) {
    developer.log('=== GENERATING SCORE CHART DATA ===', name: 'AdvancedAnalytics');
    developer.log('Days back: $daysBack, Bucket size: $bucketSize', name: 'AdvancedAnalytics');
    
    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: daysBack));
    final dataPoints = <AdvancedChartDataPoint>[];
    
    DateTime current = startDate;
    while (current.isBefore(now.add(const Duration(days: 1)))) {
      final score = computeHabitStrengthScore(current);
      
      dataPoints.add(AdvancedChartDataPoint(
        date: current,
        value: score,
        label: _formatDateLabel(current, bucketSize),
      ));
      
      // Move to next bucket
      current = current.add(Duration(days: bucketSize));
    }
    
    developer.log('Generated ${dataPoints.length} score data points', name: 'AdvancedAnalytics');
    developer.log('DEBUG Score Chart Data: ${dataPoints.map((p) => '${p.date.toIso8601String()}: ${p.value}').toList()}', name: 'AdvancedAnalytics');
    
    return dataPoints;
  }
  
  /// PHASE 1: Generate History Grid Data
  /// Returns calendar-style data for the new History Chart
  Map<DateTime, HistoryState> generateHistoryGridData({
    int daysBack = 90,
  }) {
    developer.log('=== GENERATING HISTORY GRID DATA ===', name: 'AdvancedAnalytics');
    developer.log('Days back: $daysBack', name: 'AdvancedAnalytics');
    
    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: daysBack));
    final historyData = <DateTime, HistoryState>{};
    
    DateTime current = startDate;
    while (current.isBefore(now.add(const Duration(days: 1)))) {
      final dateKey = DateTime(current.year, current.month, current.day);
      
      // Check if we have an entry for this date
      final entryForDate = entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return entryDate == dateKey;
      }).firstOrNull;
      
      HistoryState state;
      if (entryForDate != null) {
        state = _computeHistoryState(entryForDate);
      } else {
        // No entry for this date
        state = HistoryState.NOT_COMPLETED;
      }
      
      historyData[dateKey] = state;
      current = current.add(const Duration(days: 1));
    }
    
    developer.log('Generated history data for ${historyData.length} dates', name: 'AdvancedAnalytics');
    developer.log('DEBUG History Grid Data: ${historyData.entries.map((e) => '${e.key.toIso8601String()}: ${e.value}').toList()}', name: 'AdvancedAnalytics');
    
    return historyData;
  }
  
  /// Convert entry to checkmark value (0.0 to 1.0)
  double _getCheckmarkValue(Entry entry) {
    if (entry.type == EntryType.boolean) {
      return entry.boolValue ? 1.0 : 0.0;
    } else if (entry.type == EntryType.numerical && habit.targetValue != null) {
      final value = entry.numericalValue ?? 0.0;
      final percentage = (value / habit.targetValue!).clamp(0.0, 1.0);
      return percentage;
    }
    return 0.0;
  }
  
  /// Compute history state for an entry
  HistoryState _computeHistoryState(Entry entry) {
    if (entry.type == EntryType.boolean) {
      return entry.boolValue ? HistoryState.COMPLETED : HistoryState.NOT_COMPLETED;
    } else if (entry.type == EntryType.numerical && habit.targetValue != null) {
      final value = entry.numericalValue ?? 0.0;
      final percentage = value / habit.targetValue!;
      
      if (percentage >= 1.0) {
        return HistoryState.COMPLETED;
      } else if (percentage >= 0.5) {
        return HistoryState.PARTIALLY_COMPLETED;
      } else {
        return HistoryState.NOT_COMPLETED;
      }
    }
    return HistoryState.NOT_APPLICABLE;
  }
  
  /// Format date label based on bucket size
  String _formatDateLabel(DateTime date, int bucketSize) {
    if (bucketSize == 1) {
      // Daily: show month/day
      return '${date.month}/${date.day}';
    } else if (bucketSize == 7) {
      // Weekly: show week number
      final weekNumber = _getWeekOfYear(date);
      return 'W$weekNumber';
    } else if (bucketSize >= 30) {
      // Monthly: show month name
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return months[date.month - 1];
    }
    return '${date.month}/${date.day}';
  }
  
  /// Get week number of year
  int _getWeekOfYear(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).ceil() + 1;
  }
  
  /// PHASE 3: Recomputation trigger
  /// Call this whenever entries are added, edited, or deleted
  void triggerRecomputation() {
    developer.log('=== TRIGGERING RECOMPUTATION ===', name: 'AdvancedAnalytics');
    
    // Clear caches
    _scoreCache.clear();
    _historyCache.clear();
    
    // Reinitialize
    _initializeCache();
    
    developer.log('Recomputation completed', name: 'AdvancedAnalytics');
  }
  
  /// Get current habit strength (0-100%)
  int getCurrentHabitStrength() {
    final score = computeHabitStrengthScore(DateTime.now());
    return (score * 100).round();
  }
  
  /// Get completion rate for last N days
  double getCompletionRate({int days = 30}) {
    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: days));
    
    int totalDays = 0;
    int completedDays = 0;
    
    DateTime current = startDate;
    while (current.isBefore(now.add(const Duration(days: 1)))) {
      final dateKey = DateTime(current.year, current.month, current.day);
      
      final entryForDate = entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return entryDate == dateKey;
      }).firstOrNull;
      
      totalDays++;
      if (entryForDate != null && _getCheckmarkValue(entryForDate) >= 0.8) {
        completedDays++;
      }
      
      current = current.add(const Duration(days: 1));
    }
    
    return totalDays > 0 ? (completedDays / totalDays) : 0.0;
  }
}